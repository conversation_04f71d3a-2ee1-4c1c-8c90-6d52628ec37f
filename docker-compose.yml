version: '3.8'

services:
  codebuddy_api:
    build: .
    container_name: codebuddy_api
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    volumes:
      - ./client.json:/app/client.json
      - ./models.json:/app/models.json
      - ./codebuddy.json:/app/codebuddy.json
    networks:
      - codebuddy_net

  proxy:
    build: .
    container_name: proxy
    command: ["uvicorn", "format_proxy:app", "--host", "0.0.0.0", "--port", "8080"]
    ports:
      - "8080:8080"
    depends_on:
      - codebuddy_api
    environment:
      - BACKEND_TYPE=openai
      - BACKEND_BASE_URL=http://codebuddy_api:8000
      - PROXY_PORT=8080
    networks:
      - codebuddy_net

networks:
  codebuddy_net:
    driver: bridge