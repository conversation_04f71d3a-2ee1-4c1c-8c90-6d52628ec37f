import asyncio
import json
import time
from contextlib import asynccontextmanager
from itertools import cycle
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import aiofiles
import httpx
from fastapi import FastAPI, Request, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field


class ConfigManager:
    def __init__(self):
        self.auth_tokens = []
        self.token_cycle = None
        self.models_map = {}
        self.api_keys = []
        
    async def load_configs(self):
        async with aiofiles.open("codebuddy.json", "r") as f:
            data = json.loads(await f.read())
            self.auth_tokens = [item["authorization"] for item in data]
            self.token_cycle = cycle(self.auth_tokens)
            
        async with aiofiles.open("models.json", "r") as f:
            self.models_map = json.loads(await f.read())
            
        async with aiofiles.open("client.json", "r") as f:
            self.api_keys = json.loads(await f.read())
    
    def get_next_token(self):
        return next(self.token_cycle)
        
    def validate_api_key(self, api_key):
        return api_key in self.api_keys


class ContentItem(BaseModel):
    type: str
    text: str


class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[ContentItem]]


class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = False
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    tools: Optional[List[Dict[str, Any]]] = None


def get_codebuddy_headers(authorization: str) -> Dict[str, str]:
    return {
        "User-Agent": "CodeBuddyIDE/0.1.14",
        "Connection": "close",
        "Accept": "application/json",
        "Accept-Encoding": "gzip,deflate",
        "Content-Type": "application/json",
        "x-stainless-lang": "js",
        "x-stainless-package-version": "4.96.0",
        "x-stainless-os": "Windows",
        "x-stainless-arch": "x64",
        "x-stainless-runtime": "node",
        "x-stainless-runtime-version": "v20.19.0",
        "authorization": f"Bearer {authorization}",
        "x-domain": "www.codebuddy.ai",
        "x-conversation-request-id": f"r-3742-{int(time.time() * 1000)}",
        "x-agent-intent": "craft",
        "x-stainless-retry-count": "0",
        "x-stainless-timeout": "600",
    }


config_manager = ConfigManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    await config_manager.load_configs()
    yield


app = FastAPI(lifespan=lifespan)


@app.get("/v1/models")
async def list_models():
    current_time = int(time.time())
    models_data = []
    
    for model_id in config_manager.models_map:
        models_data.append({
            "id": model_id,
            "object": "model",
            "created": current_time,
            "owned_by": "anthropic"
        })
    
    return {"object": "list", "data": models_data}


@app.post("/v1/chat/completions")
async def chat_completions(request: Request):
    # 验证API密钥
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return Response(
            content=json.dumps({"error": "Missing Authorization header"}),
            status_code=401,
            media_type="application/json"
        )
    
    api_key = auth_header.replace("Bearer ", "") if auth_header.startswith("Bearer ") else auth_header
    if not config_manager.validate_api_key(api_key):
        return Response(
            content=json.dumps({"error": "Invalid API key"}),
            status_code=401,
            media_type="application/json"
        )
    
    body = await request.json()
    
    # 从请求中获取模型ID并映射到CodeBuddy内部模型名称
    model_id = body.get("model")
    if model_id not in config_manager.models_map:
        return Response(
            content=json.dumps({"error": f"Model {model_id} not found"}),
            status_code=404,
            media_type="application/json"
        )
    
    # 替换模型ID
    body["model"] = config_manager.models_map[model_id]
    
    # 替换messages中的system prompt
    messages = body.get("messages", [])
    system = None
    for m in messages:
        if m["role"] == "system":
            system = m["content"]
            break
    if system is None:
        messages.insert(0, {"role": "system", "content": '.'})
        body["messages"] = messages
    
    # 获取下一个可用的认证令牌
    auth_token = config_manager.get_next_token()
    
    # 构建请求头
    headers = get_codebuddy_headers(auth_token)
    
    # 确定是否为流式请求
    is_stream = body.get("stream", False)
    
    url = "https://www.codebuddy.ai/v2/chat/completions"
    
    if is_stream:
        async def stream_response_generator():
            async with httpx.AsyncClient() as client:
                async with client.stream(
                    "POST",
                    url,
                    json=body,
                    headers=headers,
                    timeout=600
                ) as response:
                    async for chunk in response.aiter_bytes():
                        yield chunk
        
        return StreamingResponse(
            stream_response_generator(),
            media_type="text/event-stream"
        )
    else:
        body["stream"] = True
        async with httpx.AsyncClient() as client:
            response_id = None
            response_model = body["model"]
            content_parts = []
            tool_calls = []
            finish_reason = None
            usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
            response_status = 200
            
            async with client.stream(
                "POST",
                url,
                json=body,
                headers=headers,
                timeout=600
            ) as response:
                response_status = response.status_code
                if response_status != 200:
                    return Response(
                        content=await response.read(),
                        status_code=response_status,
                        media_type="application/json"
                    )
                
                async for line in response.aiter_lines():
                    if not line or not line.startswith("data: "):
                        continue
                    
                    if line == "data: [DONE]":
                        break
                    
                    try:
                        data = json.loads(line[6:])  # Remove "data: " prefix
                        
                        # Extract response ID from first chunk
                        if response_id is None:
                            response_id = data.get("id")
                        
                        # Process choices
                        for choice in data.get("choices", []):
                            delta = choice.get("delta", {})
                            
                            # Collect content
                            if "content" in delta and delta["content"]:
                                content_parts.append(delta["content"])
                            
                            # Collect tool calls
                            if "tool_calls" in delta and delta["tool_calls"]:
                                for tool_call in delta["tool_calls"]:
                                    # Find existing tool call to update or add new one
                                    if tool_call.get("index") is not None:
                                        idx = tool_call.get("index")
                                        while len(tool_calls) <= idx:
                                            tool_calls.append({"type": "function", "function": {"name": "", "arguments": ""}})
                                        
                                        # Update function name if present
                                        if "function" in tool_call:
                                            if "name" in tool_call["function"] and tool_call["function"]["name"]:
                                                tool_calls[idx]["function"]["name"] = tool_call["function"]["name"]
                                            
                                            # Append to arguments if present
                                            if "arguments" in tool_call["function"] and tool_call["function"]["arguments"] is not None:
                                                tool_calls[idx]["function"]["arguments"] += tool_call["function"]["arguments"]
                                        
                                        # Add ID and type if present
                                        if "id" in tool_call:
                                            tool_calls[idx]["id"] = tool_call["id"]
                                        if "type" in tool_call:
                                            tool_calls[idx]["type"] = tool_call["type"]
                            
                            # Get finish reason from last chunk
                            if "finish_reason" in choice and choice["finish_reason"]:
                                finish_reason = choice["finish_reason"]
                        
                        # Update usage stats from the last chunk
                        if "usage" in data:
                            usage = data["usage"]
                    except json.JSONDecodeError:
                        continue
            
            # Construct final response in OpenAI format
            final_response = {
                "id": response_id or f"chatcmpl-{int(time.time() * 1000)}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": model_id,  # Use original model ID requested by client
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": "".join(content_parts) if not tool_calls else None,
                            "tool_calls": tool_calls if tool_calls else None
                        },
                        "finish_reason": finish_reason or "stop"
                    }
                ],
                "usage": usage
            }
            
            # Remove None values for cleaner JSON
            if final_response["choices"][0]["message"]["content"] is None:
                del final_response["choices"][0]["message"]["content"]
            if final_response["choices"][0]["message"]["tool_calls"] is None:
                del final_response["choices"][0]["message"]["tool_calls"]
            
            return Response(
                content=json.dumps(final_response),
                status_code=200,
                media_type="application/json"
            )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)